#!/usr/bin/env python3

def test_account_display():
    """Test account status display with sample data"""
    
    # Sample futures balance data (from real API response)
    futures_balance = [
        {
            "currency": "USDT",
            "positionMargin": 3.784,
            "availableBalance": 434.*********,
            "cashBalance": 430.********,
            "frozenBalance": 67.2384,
            "equity": 504.********,
            "unrealized": -0.80248,
            "bonus": 4
        }
    ]

    # Test with your example values
    example_balance = [
        {
            "currency": "USDT",
            "equity": 505.04,  # Giá trị tài sản
            "cashBalance": 505.79,  # Số dư ví
            "unrealized": -0.75
        }
    ]
    
    print("🧪 TESTING ACCOUNT STATUS DISPLAY")
    print("=" * 50)

    # Test with real API data
    print("📊 TEST 1: Real API Data")
    asset_value = 0.0
    wallet_balance = 0.0

    for asset in futures_balance:
        if asset.get('currency') == 'USDT':
            asset_value = float(asset.get('equity', 0))  # Gi<PERSON> trị tài sản (bao gồm P&L)
            wallet_balance = float(asset.get('cashBalance', 0))  # Số dư ví (không bao gồm P&L)
            break

    # Display format
    trading_content = f"""```
💰 Account Status (Futures)
Giá trị tài sản:    ${asset_value:>8,.2f}
Số dư ví:           ${wallet_balance:>8,.2f}
```"""

    print(trading_content)

    # Test with your example values
    print("📊 TEST 2: Your Example Values")
    for asset in example_balance:
        if asset.get('currency') == 'USDT':
            asset_value = float(asset.get('equity', 0))
            wallet_balance = float(asset.get('cashBalance', 0))
            break

    example_content = f"""```
💰 Account Status (Futures)
Giá trị tài sản:    ${asset_value:>8,.2f}
Số dư ví:           ${wallet_balance:>8,.2f}
```"""

    print(example_content)
    
    print("📊 COMPARISON:")
    print(f"  Giá trị tài sản (equity): ${asset_value:.2f}")
    print(f"  Số dư ví (cashBalance): ${wallet_balance:.2f}")
    print(f"  Unrealized P&L: ${float(futures_balance[0]['unrealized']):.2f}")
    print(f"  Available Balance: ${float(futures_balance[0]['availableBalance']):.2f}")
    
    # Verify calculation
    expected_equity = wallet_balance + float(futures_balance[0]['unrealized']) + float(futures_balance[0]['bonus'])
    print(f"\n🔍 VERIFICATION:")
    print(f"  Cash Balance: ${wallet_balance:.2f}")
    print(f"  + Unrealized P&L: ${float(futures_balance[0]['unrealized']):.2f}")
    print(f"  + Bonus: ${float(futures_balance[0]['bonus']):.2f}")
    print(f"  = Expected Equity: ${expected_equity:.2f}")
    print(f"  Actual Equity: ${asset_value:.2f}")
    print(f"  Match: {'✅' if abs(expected_equity - asset_value) < 0.01 else '❌'}")

if __name__ == "__main__":
    test_account_display()
