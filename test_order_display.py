#!/usr/bin/env python3

def test_order_display():
    """Test order display with real data"""
    
    # Real order data from API
    orders = [
        {
            "symbol": "PAXG_USDT",
            "side": 3,  # This should be SHORT
            "orderType": 1,
            "price": 3363.35,
            "vol": 1000,  # 1000 contracts = 1.0 PAXG
            "positionId": 0
        },
        {
            "symbol": "PAXG_USDT", 
            "side": 1,  # This should be LONG
            "orderType": 1,
            "price": 3296.00,
            "vol": 1000,  # 1000 contracts = 1.0 PAXG
            "positionId": 0
        },
        {
            "symbol": "BTC_USDT",
            "side": 2,  # This should be SHORT (TP order)
            "orderType": 1,
            "price": 113666.00,
            "vol": 8,  # 8 contracts = 0.008 BTC
            "positionId": 944466963  # This belongs to a position (TP/SL)
        }
    ]
    
    print("🧪 TESTING ORDER DISPLAY")
    print("=" * 50)
    
    # Filter new orders only (positionId == 0)
    new_orders = [order for order in orders if order.get('positionId', 0) == 0]
    
    print("📋 Pending Orders")
    for order in new_orders:
        symbol = order.get('symbol', 'N/A')
        side_code = order.get('side', 1)
        order_type_code = order.get('orderType', 1)
        
        # Determine order side
        if side_code == 1:
            order_label = "LONG"
        elif side_code == 2:
            order_label = "SHORT"
        elif side_code == 3:
            order_label = "SHORT"  # Side code 3 is also SHORT
        else:
            order_label = "UNKNOWN"
        
        order_type = 'LIMIT' if order_type_code == 1 else 'MARKET'
        price = float(order.get('price', 0))
        volume_contracts = float(order.get('vol', 0))
        
        # Convert volume from contracts to actual asset amount
        if symbol == 'BTC_USDT':
            volume_asset = volume_contracts * 0.001
            asset_unit = 'BTC'
        elif symbol == 'PAXG_USDT':
            volume_asset = volume_contracts / 1000.0
            asset_unit = 'PAXG'
        else:
            volume_asset = volume_contracts * 0.001
            asset_unit = symbol.split('_')[0]
        
        # Format volume: use .1f for whole numbers, .4f for decimals
        if volume_asset == int(volume_asset):
            vol_format = f"{volume_asset:.1f}"
        else:
            vol_format = f"{volume_asset:.4f}"
        
        print(f"{symbol} {order_label} {order_type}")
        print(f"Price: ${price:.2f} | Vol: {vol_format} {asset_unit}")
        print()
    
    print("🔍 SIDE CODE MAPPING:")
    print("  Side Code 1: LONG")
    print("  Side Code 2: SHORT") 
    print("  Side Code 3: SHORT")
    
    print(f"\n📊 SUMMARY:")
    print(f"  Total orders: {len(orders)}")
    print(f"  New orders (positionId=0): {len(new_orders)}")
    print(f"  TP/SL orders (positionId!=0): {len(orders) - len(new_orders)}")

if __name__ == "__main__":
    test_order_display()
