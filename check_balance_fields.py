#!/usr/bin/env python3
import asyncio
import json
from mexc_client import MEXCClient

async def check_balance_fields():
    print("🔍 CHECKING FUTURES BALANCE FIELDS...")
    print("=" * 60)
    
    async with MEXCClient() as client:
        # Check Futures Balance
        print("\n💰 FUTURES BALANCE FIELDS:")
        balance = await client.get_futures_account_assets()
        if balance:
            print("Raw balance data:")
            print(json.dumps(balance, indent=2))
            
            # Check USDT asset specifically
            for asset in balance:
                if asset.get('currency') == 'USDT':
                    print(f"\n📊 USDT Asset Fields:")
                    for key, value in asset.items():
                        print(f"  {key}: {value}")
                    break
        else:
            print("No balance data found")
        
        print("\n" + "=" * 60)

if __name__ == "__main__":
    asyncio.run(check_balance_fields())
