#!/usr/bin/env python3

def test_tp_sl_identification():
    """Test TP/SL identification logic with sample data"""
    
    # Sample position data (SHORT position)
    position = {
        'symbol': 'BTC_USDT',
        'positionType': 2,  # SHORT
        'positionId': 12345,
        'holdVol': 8.0,  # 8 contracts = 0.008 PAXG
        'holdAvgPrice': 118250.00,  # Entry price
        'leverage': 25
    }
    
    # Sample orders data
    orders = [
        {
            'symbol': 'BTC_USDT',
            'positionId': 12345,  # Belongs to position
            'price': 113666.00,   # Lower than entry (TP for SHORT)
            'vol': 8.0,
            'side': 4  # Close position order
        },
        {
            'symbol': 'PAXG_USDT',
            'positionId': 0,      # New order (not TP/SL)
            'price': 3296.00,
            'vol': 1000.0,
            'side': 1  # LONG order
        }
    ]
    
    print("🧪 TESTING TP/SL IDENTIFICATION LOGIC")
    print("=" * 50)
    
    # Process position
    side = 'SHORT' if position['positionType'] == 2 else 'LONG'
    entry_price = position['holdAvgPrice']
    position_id = position['positionId']
    
    print(f"📊 Position: {position['symbol']} {side}")
    print(f"   Entry Price: ${entry_price:.2f}")
    print(f"   Position ID: {position_id}")
    print()
    
    # Find TP/SL orders
    tp_orders = []
    sl_orders = []
    new_orders = []
    
    for order in orders:
        order_position_id = order.get('positionId', 0)
        price = order['price']
        
        if order_position_id == position_id:
            # This is a TP/SL order for the position
            if side == 'SHORT':
                if price < entry_price:
                    tp_orders.append(order)
                    print(f"✅ TP Order: {order['symbol']} @ ${price:.2f} (< ${entry_price:.2f})")
                elif price > entry_price:
                    sl_orders.append(order)
                    print(f"🛑 SL Order: {order['symbol']} @ ${price:.2f} (> ${entry_price:.2f})")
            elif side == 'LONG':
                if price > entry_price:
                    tp_orders.append(order)
                    print(f"✅ TP Order: {order['symbol']} @ ${price:.2f} (> ${entry_price:.2f})")
                elif price < entry_price:
                    sl_orders.append(order)
                    print(f"🛑 SL Order: {order['symbol']} @ ${price:.2f} (< ${entry_price:.2f})")
        elif order_position_id == 0:
            # This is a new order
            new_orders.append(order)
            print(f"🆕 New Order: {order['symbol']} @ ${price:.2f}")
    
    print()
    print("📈 SUMMARY:")
    print(f"   TP Orders: {len(tp_orders)}")
    print(f"   SL Orders: {len(sl_orders)}")
    print(f"   New Orders: {len(new_orders)}")
    print()
    
    # Test counting logic
    print("📊 DASHBOARD COUNTS:")
    print(f"   Open Positions: 1")
    print(f"   Pending Orders: {len(new_orders)} (only new orders)")

if __name__ == "__main__":
    test_tp_sl_identification()
