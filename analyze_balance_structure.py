#!/usr/bin/env python3

def analyze_balance_structure():
    """Analyze MEXC futures balance structure"""
    
    # Real API data from previous check
    balance_data = {
        "currency": "USDT",
        "positionMargin": 3.784,
        "availableBalance": 434.776230116,
        "cashBalance": 430.77623011,
        "frozenBalance": 67.2384,
        "equity": 504.99615011,
        "unrealized": -0.80248,
        "bonus": 4
    }
    
    print("🔍 ANALYZING MEXC FUTURES BALANCE STRUCTURE")
    print("=" * 60)
    
    print("📊 Raw Data:")
    for key, value in balance_data.items():
        print(f"  {key}: {value}")
    
    print("\n🧮 CALCULATIONS:")
    
    # Extract values
    equity = balance_data["equity"]
    cash_balance = balance_data["cashBalance"]
    unrealized_pnl = balance_data["unrealized"]
    position_margin = balance_data["positionMargin"]
    frozen_balance = balance_data["frozenBalance"]
    available_balance = balance_data["availableBalance"]
    bonus = balance_data["bonus"]
    
    print(f"Equity (giá trị tài sản):     ${equity:.2f}")
    print(f"Cash Balance:                 ${cash_balance:.2f}")
    print(f"Unrealized P&L:               ${unrealized_pnl:.2f}")
    print(f"Position Margin:              ${position_margin:.2f}")
    print(f"Frozen Balance:               ${frozen_balance:.2f}")
    print(f"Available Balance:            ${available_balance:.2f}")
    print(f"Bonus:                        ${bonus:.2f}")
    
    print("\n🎯 DETERMINING WALLET BALANCE:")
    
    # Method 1: Equity - Unrealized P&L
    wallet_balance_method1 = equity - unrealized_pnl
    print(f"Method 1 (Equity - Unrealized P&L): ${wallet_balance_method1:.2f}")
    
    # Method 2: Cash Balance + Position Margin + Frozen Balance + Bonus
    wallet_balance_method2 = cash_balance + position_margin + frozen_balance + bonus
    print(f"Method 2 (Cash + Margin + Frozen + Bonus): ${wallet_balance_method2:.2f}")
    
    # Method 3: Cash Balance + Bonus (simple)
    wallet_balance_method3 = cash_balance + bonus
    print(f"Method 3 (Cash + Bonus): ${wallet_balance_method3:.2f}")
    
    print("\n✅ RECOMMENDED MAPPING:")
    print(f"Giá trị tài sản (Asset Value): equity = ${equity:.2f}")
    print(f"Số dư ví (Wallet Balance): equity - unrealized = ${wallet_balance_method1:.2f}")
    
    print("\n📋 VERIFICATION:")
    print(f"Wallet Balance + Unrealized P&L = ${wallet_balance_method1:.2f} + ${unrealized_pnl:.2f} = ${wallet_balance_method1 + unrealized_pnl:.2f}")
    print(f"Should equal Equity: ${equity:.2f}")
    print(f"Match: {'✅' if abs((wallet_balance_method1 + unrealized_pnl) - equity) < 0.01 else '❌'}")

if __name__ == "__main__":
    analyze_balance_structure()
