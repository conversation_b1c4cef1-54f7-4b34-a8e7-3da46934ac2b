import discord
from discord.ext import commands, tasks
import asyncio
import yaml
import logging
from datetime import datetime
from typing import Optional
from mexc_client import MEXCClient

logging.basicConfig(level=logging.INFO)

class PAXGBot(commands.Bot):
    def __init__(self):
        intents = discord.Intents.default()
        intents.message_content = False
        super().__init__(command_prefix='!', intents=intents)
        
        self.config = self.load_config()
        self.paxg_channel = None
        self.pinned_message = None
        self.last_price = 0
        self.last_volume = 0
        self.price_threshold = 0.5  # 0.5% thay đổi giá
        self.volume_threshold = 20  # 20% thay đổi volume
        
    def load_config(self):
        with open('config.ymal', 'r', encoding='utf-8') as file:
            return yaml.safe_load(file)
    
    async def on_ready(self):
        print(f'{self.user} đã kết nối thành công!')
        
        guild = self.get_guild(int(self.config['discord']['guild_id']))
        if guild:
            self.paxg_channel = discord.utils.get(guild.channels, name='paxg')
            if not self.paxg_channel:
                print("Không tìm thấy channel 'paxg'. Vui lòng tạo channel này.")
                return
        
        self.price_monitor.start()
        await self.send_initial_dashboard()
    
    async def send_initial_dashboard(self):
        async with MEXCClient() as client:
            combined_embed = await self.create_combined_dashboard()
            if combined_embed:
                message = await self.paxg_channel.send(embed=combined_embed)
                await message.pin()
                self.pinned_message = message

                # Update last price for monitoring
                price_data = await client.get_price_data()
                if price_data:
                    self.last_price = price_data['current_price']
                    self.last_volume = price_data['volume']
    
    def create_price_embed(self, data, is_initial=False, alert_type=None):
        current_price = data['current_price']
        day_open_price = data['day_open_price']
        day_price_change = data['day_price_change']
        day_price_change_percent = data['day_price_change_percent']
        price_change_24h = data['price_change_24h']
        price_change_percent_24h = data['price_change_percent_24h']

        color = discord.Color.green() if day_price_change >= 0 else discord.Color.red()

        if alert_type:
            if alert_type == "price":
                title = "🚨 CẢNH BÁO GIÁ PAXG"
            elif alert_type == "volume":
                title = "🚨 CẢNH BÁO VOLUME PAXG"
            else:
                title = "🚨 CẢNH BÁO PAXG"
        elif is_initial:
            title = "📊 CẬP NHẬT GIÁ PAXG"
        else:
            title = "📊 CẬP NHẬT GIÁ PAXG"

        embed = discord.Embed(title=title, color=color, timestamp=datetime.now())

        # Format giống watchlist - các cột thẳng hàng với monospace
        day_change_icon = "🔴" if day_price_change < 0 else "🟢"
        change_24h_icon = "🔴" if price_change_24h < 0 else "🟢"

        # Tạo nội dung theo format watchlist với cột thẳng hàng sử dụng monospace
        content = f"""```
🧈 Giá hiện tại      ${current_price:>8,.2f}
{day_change_icon} CHG DAY           ${abs(day_price_change):>8,.2f}
📊 Giá mở cửa ngày   ${day_open_price:>8,.2f}
{change_24h_icon} Thay đổi 24h      ${abs(price_change_24h):>8,.2f}
📊 Cao nhất 24h      ${data['high_price']:>8,.2f}
📊 Thấp nhất 24h     ${data['low_price']:>8,.2f}
💎 Volume 24h      {data['volume']:>8,.2f} PAXG
```"""

        embed.description = content
        embed.set_footer(text="MEXC Exchange • Cập nhật mỗi 30 giây")

        return embed

    async def create_combined_dashboard(self) -> Optional[discord.Embed]:
        async with MEXCClient() as client:
            # Get PAXG price data
            price_data = await client.get_price_data()
            if not price_data:
                logging.error("Failed to get PAXG price data")
                return None

            # Get account and trading data
            account_info = await client.get_account_info()
            open_orders = await client.get_open_orders()
            futures_positions = await client.get_futures_positions()
            futures_balance = await client.get_futures_account_assets()
            futures_open_orders = await client.get_futures_open_orders()

            # Create embed with PAXG price info
            current_price = price_data['current_price']
            day_open_price = price_data['day_open_price']
            day_price_change = price_data['day_price_change']
            price_change_24h = price_data['price_change_24h']

            color = discord.Color.green() if day_price_change >= 0 else discord.Color.red()

            embed = discord.Embed(
                title="📊 CẬP NHẬT GIÁ PAXG",
                color=color,
                timestamp=datetime.now()
            )

            # PAXG Price Section (giống format cũ)
            day_change_icon = "🔴" if day_price_change < 0 else "🟢"
            change_24h_icon = "🔴" if price_change_24h < 0 else "🟢"

            price_content = f"""```
🧈 Giá hiện tại      ${current_price:>8,.2f}
{day_change_icon} CHG DAY           ${abs(day_price_change):>8,.2f}
📊 Giá mở cửa ngày   ${day_open_price:>8,.2f}
{change_24h_icon} Thay đổi 24h      ${abs(price_change_24h):>8,.2f}
📊 Cao nhất 24h      ${price_data['high_price']:>8,.2f}
📊 Thấp nhất 24h     ${price_data['low_price']:>8,.2f}
💎 Volume 24h      {price_data['volume']:>8,.2f} PAXG
```

"""

            # Trading Status Dashboard Section
            trading_content = "📊 **Trading Status Dashboard**\n"

            # Account Status - Use futures balance if available, otherwise spot balance
            futures_total_balance = 0.0
            futures_available_balance = 0.0

            if futures_balance:
                # Calculate futures balance
                for asset in futures_balance:
                    if asset.get('currency') == 'USDT':
                        futures_total_balance = float(asset.get('equity', 0))
                        futures_available_balance = float(asset.get('availableBalance', 0))
                        break

                trading_content += f"""```
💰 Account Status (Futures)
Total Balance:      ${futures_total_balance:>8,.2f}
Available Balance:  ${futures_available_balance:>8,.2f}
```"""
            elif account_info:
                # Fallback to spot balance
                total_balance = client.calculate_total_balance(account_info)
                available_balance = client.calculate_available_balance(account_info)

                trading_content += f"""```
💰 Account Status (Spot)
Total Balance:      ${total_balance:>8,.2f}
Available Balance:  ${available_balance:>8,.2f}
```"""
            else:
                trading_content += "```\n❌ Account data not available\nAPI credentials may be missing or invalid\n```"

            # P&L Summary - Use real futures data
            total_unrealized_pnl = 0.0
            active_positions_count = 0
            new_orders_count = 0

            # Count actual open positions (holdVol > 0)
            if futures_positions:
                for pos in futures_positions:
                    hold_vol = float(pos.get('holdVol', 0))
                    if hold_vol > 0:
                        active_positions_count += 1

            # Count only new orders (not TP/SL orders)
            if futures_open_orders:
                for order in futures_open_orders:
                    side_code = order.get('side', 1)
                    position_id = order.get('positionId', 0)
                    # Only count orders that are not TP/SL (side != 4 or no positionId)
                    if side_code != 4 or position_id == 0:
                        new_orders_count += 1

            # Calculate total unrealized P&L from futures balance
            if futures_balance:
                for asset in futures_balance:
                    unrealized = float(asset.get('unrealized', 0))
                    total_unrealized_pnl += unrealized

            pnl_icon = "🔴" if total_unrealized_pnl < 0 else "🟢"

            trading_content += f"""```
📈 P&L Summary
Unrealized P&L:     {pnl_icon} ${total_unrealized_pnl:>8,.2f}
Open Positions:     {active_positions_count:>8}
Pending Orders:     {new_orders_count:>8}
```"""

            # Open Positions
            if futures_positions and active_positions_count > 0:
                trading_content += "```\n📊 Open Positions\n"
                for pos in futures_positions[:3]:  # Show max 3 positions
                    hold_vol = float(pos.get('holdVol', 0))
                    if hold_vol > 0:
                        symbol = pos.get('symbol', 'N/A')
                        position_type = pos.get('positionType', 1)
                        side = 'LONG' if position_type == 1 else 'SHORT'
                        position_id = pos.get('positionId', 0)

                        # Convert volume from contracts to PAXG (1 PAXG = 1000 contracts)
                        size_contracts = hold_vol
                        size_paxg = size_contracts / 1000.0

                        entry_price = float(pos.get('holdAvgPrice', 0))
                        leverage = pos.get('leverage', 1)

                        # Calculate unrealized PnL from balance data
                        unrealized_pnl = 0.0
                        if futures_balance:
                            for asset in futures_balance:
                                if asset.get('currency') == 'USDT':
                                    unrealized_pnl = float(asset.get('unrealized', 0))
                                    break

                        pnl_icon = "🔴" if unrealized_pnl < 0 else "🟢"

                        trading_content += f"{symbol} {side} {leverage}x        {pnl_icon} ${unrealized_pnl:.4f}\n"
                        trading_content += f"Entry: ${entry_price:.2f} | Size: {size_paxg:.4f} PAXG\n"

                        # Find TP and SL orders for this position
                        tp_price = None
                        tp_vol = None
                        sl_price = None
                        sl_vol = None

                        if futures_open_orders:
                            for order in futures_open_orders:
                                order_position_id = order.get('positionId', 0)
                                if order_position_id == position_id:
                                    side_code = order.get('side', 1)
                                    if side_code == 4:  # Close position order (TP/SL)
                                        price = float(order.get('price', 0))
                                        vol_contracts = float(order.get('vol', 0))
                                        vol_paxg = vol_contracts / 1000.0

                                        # Determine if it's TP or SL based on price vs entry
                                        if (side == 'LONG' and price > entry_price) or (side == 'SHORT' and price < entry_price):
                                            tp_price = price
                                            tp_vol = vol_paxg
                                        else:
                                            sl_price = price
                                            sl_vol = vol_paxg

                        # Display TP
                        if tp_price and tp_vol:
                            trading_content += f" TP: ${tp_price:.2f} | Vol: {tp_vol:.4f} PAXG\n"
                        else:
                            trading_content += f" TP: None\n"

                        # Display SL
                        if sl_price and sl_vol:
                            trading_content += f" SL: ${sl_price:.2f} | Vol: {sl_vol:.4f} PAXG\n"
                        else:
                            trading_content += f" SL: None\n"

                        trading_content += "\n"
                trading_content += "```"
            else:
                trading_content += "```\n📊 Open Positions\nNo open positions.\n```"

            # Pending Orders - Only show new orders (not TP/SL)
            new_orders = []
            if futures_open_orders:
                for order in futures_open_orders:
                    side_code = order.get('side', 1)
                    position_id = order.get('positionId', 0)
                    # Only include orders that are not TP/SL (side != 4 or no positionId)
                    if side_code != 4 or position_id == 0:
                        new_orders.append(order)

            if new_orders:
                trading_content += "```\n📋 Pending Orders\n"
                for order in new_orders[:3]:  # Show max 3 orders
                    symbol = order.get('symbol', 'N/A')
                    side_code = order.get('side', 1)
                    order_type_code = order.get('orderType', 1)

                    # Determine order side
                    if side_code == 1:
                        order_label = "LONG"
                    elif side_code == 2:
                        order_label = "SHORT"
                    else:
                        order_label = "UNKNOWN"

                    order_type = 'LIMIT' if order_type_code == 1 else 'MARKET'
                    price = float(order.get('price', 0))
                    volume_contracts = float(order.get('vol', 0))

                    # Convert volume from contracts to PAXG (1 PAXG = 1000 contracts)
                    volume_paxg = volume_contracts / 1000.0

                    trading_content += f"{symbol} {order_label} {order_type}\n"
                    trading_content += f"Price: ${price:.2f} | Vol: {volume_paxg:.4f} PAXG\n\n"
                trading_content += "```"
            elif open_orders and len(open_orders) > 0:
                trading_content += "```\n📋 Pending Orders (Spot)\n"
                for order in open_orders[:3]:  # Show max 3 orders
                    symbol = order.get('symbol', 'N/A')
                    side = order.get('side', 'N/A')
                    order_type = order.get('type', 'N/A')
                    price = float(order.get('price', 0))
                    quantity = float(order.get('origQty', 0))
                    trading_content += f"{symbol} {side} {order_type}\n"
                    trading_content += f"Price: ${price:.4f} | Qty: {quantity:.4f}\n\n"
                trading_content += "```"
            else:
                trading_content += "```\n📋 Pending Orders\nNo new position orders.\n```"

            # Combine all content
            full_content = price_content + trading_content
            embed.description = full_content
            embed.set_footer(text="MEXC Exchange • Cập nhật mỗi 30 giây")

            return embed


    
    @tasks.loop(seconds=30)
    async def price_monitor(self):
        try:
            async with MEXCClient() as client:
                price_data = await client.get_price_data()
                if not price_data:
                    return
                
                current_price = price_data['current_price']
                current_volume = price_data['volume']

                # Sử dụng thay đổi trong ngày để monitor
                day_price_change_percent = abs(price_data['day_price_change_percent'])
                price_change_percent = abs((current_price - self.last_price) / self.last_price * 100) if self.last_price > 0 else 0
                volume_change_percent = abs((current_volume - self.last_volume) / self.last_volume * 100) if self.last_volume > 0 else 0
                
                should_alert = False
                alert_type = None
                
                if price_change_percent >= self.price_threshold:
                    should_alert = True
                    alert_type = "price"
                elif volume_change_percent >= self.volume_threshold:
                    should_alert = True
                    alert_type = "volume"
                
                if should_alert:
                    embed = self.create_price_embed(price_data, alert_type=alert_type)
                    await self.paxg_channel.send(embed=embed)
                
                if self.pinned_message:
                    combined_embed = await self.create_combined_dashboard()
                    if combined_embed:
                        await self.pinned_message.edit(embed=combined_embed)

                self.last_price = current_price
                self.last_volume = current_volume
                
        except Exception as e:
            logging.error(f"Error in price_monitor: {e}")
    
    @commands.command(name='paxg')
    async def manual_price_check(self, ctx):
        combined_embed = await self.create_combined_dashboard()
        if combined_embed:
            await ctx.send(embed=combined_embed)
        else:
            await ctx.send("❌ Không thể lấy dữ liệu từ MEXC API")
    
    @commands.command(name='threshold')
    async def set_threshold(self, ctx, price_threshold: float = None, volume_threshold: float = None):
        if ctx.author.id != int(self.config['discord']['admin_id']):
            await ctx.send("❌ Bạn không có quyền sử dụng lệnh này")
            return
        
        if price_threshold is not None:
            self.price_threshold = price_threshold
        if volume_threshold is not None:
            self.volume_threshold = volume_threshold
        
        await ctx.send(f"✅ Đã cập nhật ngưỡng cảnh báo:\n"
                      f"📊 Giá: {self.price_threshold}%\n"
                      f"📦 Volume: {self.volume_threshold}%")

def run_bot():
    bot = PAXGBot()
    bot.run(bot.config['discord']['token'])
