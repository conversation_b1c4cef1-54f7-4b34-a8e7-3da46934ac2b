#!/usr/bin/env python3
import asyncio
import json
import aiohttp
from mexc_client import MEXCClient

async def get_contract_details():
    """Get contract specifications from MEXC API"""
    try:
        async with aiohttp.ClientSession() as session:
            # Get BTC_USDT contract details
            url = "https://contract.mexc.com/api/v1/contract/detail/BTC_USDT"
            async with session.get(url) as response:
                if response.status == 200:
                    btc_data = await response.json()
                    print("📊 BTC_USDT Contract Details:")
                    if btc_data.get('success'):
                        contract_info = btc_data.get('data', {})
                        print(f"  Contract Size: {contract_info.get('contractSize', 'N/A')}")
                        print(f"  Price Scale: {contract_info.get('priceScale', 'N/A')}")
                        print(f"  Vol Scale: {contract_info.get('volScale', 'N/A')}")
                        print(f"  Min Vol: {contract_info.get('minVol', 'N/A')}")
                        print(f"  Max Vol: {contract_info.get('maxVol', 'N/A')}")
                        print(json.dumps(contract_info, indent=2))
                    else:
                        print("  Failed to get contract details")

            # Get PAXG_USDT contract details
            url = "https://contract.mexc.com/api/v1/contract/detail/PAXG_USDT"
            async with session.get(url) as response:
                if response.status == 200:
                    paxg_data = await response.json()
                    print("\n📊 PAXG_USDT Contract Details:")
                    if paxg_data.get('success'):
                        contract_info = paxg_data.get('data', {})
                        print(f"  Contract Size: {contract_info.get('contractSize', 'N/A')}")
                        print(f"  Price Scale: {contract_info.get('priceScale', 'N/A')}")
                        print(f"  Vol Scale: {contract_info.get('volScale', 'N/A')}")
                        print(f"  Min Vol: {contract_info.get('minVol', 'N/A')}")
                        print(f"  Max Vol: {contract_info.get('maxVol', 'N/A')}")
                        print(json.dumps(contract_info, indent=2))
                    else:
                        print("  Failed to get contract details")
    except Exception as e:
        print(f"Error getting contract details: {e}")

async def check_current_data():
    print("🔍 CHECKING CURRENT MEXC DATA...")
    print("=" * 60)

    # First get contract specifications
    await get_contract_details()

    async with MEXCClient() as client:
        # Check Open Positions
        print("\n📊 OPEN POSITIONS:")
        positions = await client.get_futures_positions()
        if positions:
            print(f"Found {len(positions)} open positions:")
            for i, pos in enumerate(positions, 1):
                print(f"\nPosition {i}:")
                print(json.dumps(pos, indent=2))
        else:
            print("No open positions found")

        # Check Open Orders
        print("\n📋 OPEN ORDERS:")
        orders = await client.get_futures_open_orders()
        if orders:
            print(f"Found {len(orders)} open orders:")
            for i, order in enumerate(orders, 1):
                print(f"\nOrder {i}:")
                print(json.dumps(order, indent=2))
        else:
            print("No open orders found")

        # Check Account Balance
        print("\n💰 ACCOUNT BALANCE:")
        balance = await client.get_futures_account_assets()
        if balance:
            for asset in balance:
                if asset.get('currency') == 'USDT':
                    print("USDT Balance:")
                    print(json.dumps(asset, indent=2))
                    break

        print("\n" + "=" * 60)

if __name__ == "__main__":
    asyncio.run(check_current_data())
