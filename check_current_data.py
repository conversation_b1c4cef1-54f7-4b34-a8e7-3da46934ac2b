#!/usr/bin/env python3
import asyncio
import json
from mexc_client import MEXCClient

async def check_current_data():
    print("🔍 CHECKING CURRENT MEXC DATA...")
    print("=" * 60)
    
    async with MEXCClient() as client:
        # Check Open Positions
        print("\n📊 OPEN POSITIONS:")
        positions = await client.get_futures_positions()
        if positions:
            print(f"Found {len(positions)} open positions:")
            for i, pos in enumerate(positions, 1):
                print(f"\nPosition {i}:")
                print(json.dumps(pos, indent=2))
        else:
            print("No open positions found")
        
        # Check Open Orders
        print("\n📋 OPEN ORDERS:")
        orders = await client.get_futures_open_orders()
        if orders:
            print(f"Found {len(orders)} open orders:")
            for i, order in enumerate(orders, 1):
                print(f"\nOrder {i}:")
                print(json.dumps(order, indent=2))
        else:
            print("No open orders found")
        
        # Check Account Balance
        print("\n💰 ACCOUNT BALANCE:")
        balance = await client.get_futures_account_assets()
        if balance:
            for asset in balance:
                if asset.get('currency') == 'USDT':
                    print("USDT Balance:")
                    print(json.dumps(asset, indent=2))
                    break
        
        print("\n" + "=" * 60)

if __name__ == "__main__":
    asyncio.run(check_current_data())
