#!/usr/bin/env python3
"""
MEXC Futures Client - Optimized for Futures Trading Only

This is a streamlined version of the MEXC client that focuses exclusively
on futures trading operations. All spot trading functionality has been
removed for better performance and maintainability.

Author: Trading Bot System
Version: 2.0 (Futures Only)
"""

import aiohttp
import asyncio
import hashlib
import hmac
import json
import logging
import time
from typing import Dict, Optional, List
import yaml

class MEXCFuturesClient:
    """
    Optimized MEXC Futures Trading Client
    
    Features:
    - Futures positions management
    - Futures account balance
    - Futures open orders
    - Price data retrieval
    - Streamlined API calls
    """
    
    def __init__(self):
        self.base_url = "https://api.mexc.com"
        self.futures_base_url = "https://contract.mexc.com"
        self.session = None
        self.api_key = None
        self.api_secret = None
        self.load_config()

    def load_config(self):
        """Load trading configuration from config.ymal"""
        try:
            with open('config.ymal', 'r', encoding='utf-8') as file:
                config = yaml.safe_load(file)
                mexc_config = config.get('mexc', {})
                self.api_key = mexc_config.get('api_key')
                self.api_secret = mexc_config.get('api_secret')
        except Exception as e:
            logging.warning(f"Could not load MEXC config: {e}")

    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()

    def _get_headers(self, signed: bool = False) -> Dict[str, str]:
        """Get request headers"""
        headers = {'Content-Type': 'application/json'}
        if signed and self.api_key:
            headers['ApiKey'] = self.api_key
        return headers

    def _generate_signature(self, query_string: str) -> str:
        """Generate HMAC SHA256 signature"""
        if not self.api_secret:
            return ""
        return hmac.new(
            self.api_secret.encode('utf-8'),
            query_string.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()

    def _generate_futures_signature(self, access_key: str, req_time: str, request_param: str = "") -> str:
        """Generate futures API signature"""
        if not self.api_secret:
            return ""
        sign_str = access_key + req_time + request_param
        return hmac.new(
            self.api_secret.encode('utf-8'),
            sign_str.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()

    async def get_price_data(self) -> Optional[Dict]:
        """Get PAXG price data from MEXC"""
        try:
            url = f"{self.base_url}/api/v3/ticker/24hr"
            params = {"symbol": "PAXGUSDT"}
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    return self._format_price_data(data)
                else:
                    logging.error(f"Error getting price data: {response.status}")
                    return None
        except Exception as e:
            logging.error(f"Exception in get_price_data: {e}")
            return None

    def _format_price_data(self, ticker_data: Dict) -> Dict:
        """Format price data for display"""
        def safe_float(value, default=0.0):
            try:
                return float(value) if value else default
            except (ValueError, TypeError):
                return default

        return {
            "symbol": ticker_data.get("symbol", "PAXGUSDT"),
            "price": safe_float(ticker_data.get("lastPrice")),
            "change_24h": safe_float(ticker_data.get("priceChange")),
            "change_percent_24h": safe_float(ticker_data.get("priceChangePercent")),
            "high_24h": safe_float(ticker_data.get("highPrice")),
            "low_24h": safe_float(ticker_data.get("lowPrice")),
            "volume_24h": safe_float(ticker_data.get("volume")),
            "quote_volume": safe_float(ticker_data.get("quoteVolume"))
        }

    async def get_futures_positions(self, symbol: str = None) -> Optional[List]:
        """
        Get futures positions
        
        Args:
            symbol: Optional symbol filter
            
        Returns:
            List of position data
        """
        if not self.api_key or not self.api_secret:
            return []

        try:
            url = f"{self.futures_base_url}/api/v1/private/position/open_positions"
            
            data_original = {}
            if symbol:
                data_original["symbol"] = symbol

            req_time = str(int(time.time() * 1000))
            request_param = json.dumps(data_original) if data_original else ""
            signature = self._generate_futures_signature(self.api_key, req_time, request_param)

            headers = {
                'ApiKey': self.api_key,
                'Request-Time': req_time,
                'Signature': signature,
                'Content-Type': 'application/json'
            }

            async with self.session.post(url, headers=headers, json=data_original) as response:
                if response.status == 200:
                    result = await response.json()
                    return result.get('data', []) if result.get('success') else []
                else:
                    logging.error(f"Error getting futures positions: {response.status}")
                    return []
        except Exception as e:
            logging.error(f"Exception in get_futures_positions: {e}")
            return []

    async def get_futures_account_assets(self) -> Optional[List]:
        """
        Get futures account balance
        
        Returns:
            List of account asset data
        """
        if not self.api_key or not self.api_secret:
            return []

        try:
            url = f"{self.futures_base_url}/api/v1/private/account/assets"
            
            req_time = str(int(time.time() * 1000))
            signature = self._generate_futures_signature(self.api_key, req_time, "")

            headers = {
                'ApiKey': self.api_key,
                'Request-Time': req_time,
                'Signature': signature,
                'Content-Type': 'application/json'
            }

            async with self.session.post(url, headers=headers, json={}) as response:
                if response.status == 200:
                    result = await response.json()
                    return result.get('data', []) if result.get('success') else []
                else:
                    logging.error(f"Error getting futures account assets: {response.status}")
                    return []
        except Exception as e:
            logging.error(f"Exception in get_futures_account_assets: {e}")
            return []

    async def get_futures_open_orders(self, symbol: str = None) -> Optional[List]:
        """
        Get futures open orders
        
        Args:
            symbol: Optional symbol filter
            
        Returns:
            List of open order data
        """
        if not self.api_key or not self.api_secret:
            return []

        try:
            url = f"{self.futures_base_url}/api/v1/private/order/list/open_orders"
            
            data_original = {}
            if symbol:
                data_original["symbol"] = symbol

            req_time = str(int(time.time() * 1000))
            request_param = json.dumps(data_original) if data_original else ""
            signature = self._generate_futures_signature(self.api_key, req_time, request_param)

            headers = {
                'ApiKey': self.api_key,
                'Request-Time': req_time,
                'Signature': signature,
                'Content-Type': 'application/json'
            }

            async with self.session.post(url, headers=headers, json=data_original) as response:
                if response.status == 200:
                    result = await response.json()
                    return result.get('data', []) if result.get('success') else []
                else:
                    logging.error(f"Error getting futures open orders: {response.status}")
                    return []
        except Exception as e:
            logging.error(f"Exception in get_futures_open_orders: {e}")
            return []

# Alias for backward compatibility
MEXCClient = MEXCFuturesClient
