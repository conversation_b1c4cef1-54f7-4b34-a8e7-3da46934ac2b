#!/usr/bin/env python3
"""
MEXC FUTURES API COMPLETE MAPPING
Based on official documentation: https://mexcdevelop.github.io/apidocs/contract_v1_en/
"""

class MEXCFuturesMapping:
    """Complete mapping for MEXC Futures API parameters"""
    
    # POSITION PARAMETERS
    POSITION_TYPE = {
        1: "LONG",
        2: "SHORT"
    }
    
    OPEN_TYPE = {
        1: "ISOLATED",  # Isolated margin
        2: "CROSS"      # Cross margin
    }
    
    POSITION_OPEN_TYPE = {
        1: "ISOLATED_ONLY",
        2: "CROSS_ONLY", 
        3: "BOTH"  # Both isolated and cross
    }
    
    POSITION_STATE = {
        1: "HOLDING",           # Normal holding
        2: "SYSTEM_HOLDING",    # System auto-holding
        3: "CLOSED"             # Closed
    }
    
    # ORDER PARAMETERS
    ORDER_SIDE = {
        1: "OPEN_LONG",   # Open long position
        2: "CLOSE_SHORT", # Close short position
        3: "OPEN_SHORT",  # Open short position
        4: "CLOSE_LONG"   # Close long position
    }
    
    ORDER_TYPE = {
        1: "LIMIT",              # Limit order
        2: "POST_ONLY_MAKER",    # Post Only Maker
        3: "IMMEDIATE_OR_CANCEL", # IOC
        4: "FILL_OR_KILL",       # FOK
        5: "MARKET"              # Market order
    }
    
    ORDER_STATE = {
        1: "UNINFORMED",    # Not filled
        2: "UNCOMPLETED",   # Partially filled
        3: "COMPLETED",     # Fully filled
        4: "CANCELLED",     # Cancelled
        5: "INVALID"        # Invalid
    }
    
    ORDER_CATEGORY = {
        1: "LIMIT_ORDER",           # Normal limit order
        2: "SYSTEM_TAKEOVER",       # System takeover order
        3: "CLOSE_ORDER",           # Close order
        4: "ADL_REDUCTION"          # ADL reduction order
    }
    
    # CONTRACT PARAMETERS
    CONTRACT_STATE = {
        0: "ENABLED",    # Normal trading
        1: "DELIVERY",   # In delivery
        2: "COMPLETED",  # Delivery completed
        3: "OFFLINE",    # Offline
        4: "PAUSE"       # Paused
    }
    
    # TRIGGER ORDER PARAMETERS
    TRIGGER_TYPE = {
        1: "GREATER_EQUAL",  # Greater than or equal
        2: "LESS_EQUAL"      # Less than or equal
    }
    
    TRIGGER_PRICE_TYPE = {
        1: "LATEST_PRICE",  # Latest price
        2: "FAIR_PRICE",    # Fair price
        3: "INDEX_PRICE"    # Index price
    }
    
    EXECUTION_CYCLE = {
        1: "24_HOURS",  # 24 hours
        2: "7_DAYS"     # 7 days
    }
    
    # ERROR CODES
    ERROR_CODES = {
        0: "SUCCESS",
        1: "PARAM_INVALID",
        2: "INSUFFICIENT_BALANCE",
        3: "POSITION_NOT_EXISTS",
        4: "POSITION_NOT_ENOUGH",
        5: "POSITION_LIQUIDATED",
        6: "ORDER_LIQUIDATED",
        7: "RISK_LEVEL_LIMIT",
        8: "SYSTEM_CANCEL",
        9: "POSITION_MODE_NOT_MATCH",
        10: "REDUCE_ONLY_LIQUIDATED",
        11: "CONTRACT_NOT_ENABLED",
        12: "DELIVERY_CANCEL",
        13: "POSITION_LIQUIDATED_CANCEL",
        14: "ADL_CANCEL",
        15: "BLACK_USER_CANCEL",
        16: "SETTLE_FUNDING_CANCEL",
        17: "POSITION_IM_CHANGE_CANCEL",
        18: "IOC_CANCEL",
        19: "FOK_CANCEL",
        20: "POST_ONLY_CANCEL",
        21: "MARKET_CANCEL"
    }
    
    # TRANSACTION DIRECTION
    DEAL_TYPE = {
        1: "BUY",   # Purchase
        2: "SELL"   # Sell
    }
    
    DEAL_OPEN_POSITION = {
        1: "OPEN",      # Open position
        2: "CLOSE",     # Close position
        3: "NO_CHANGE"  # No position change
    }
    
    DEAL_SELF_TRADE = {
        1: "YES",  # Self trade
        2: "NO"    # Not self trade
    }
    
    # POSITION MODE
    POSITION_MODE = {
        1: "HEDGE",    # Hedge mode (can hold both long and short)
        2: "ONE_WAY"   # One-way mode (only one direction)
    }
    
    # ADL LEVEL
    ADL_LEVEL = {
        1: "LEVEL_1",  # Lowest priority
        2: "LEVEL_2",
        3: "LEVEL_3", 
        4: "LEVEL_4",
        5: "LEVEL_5"   # Highest priority
    }
    
    # RISK LIMIT TYPE
    RISK_LIMIT_TYPE = {
        "BY_VOLUME": "By trading volume",
        "BY_VALUE": "By position value"
    }

    @staticmethod
    def get_position_type_name(position_type: int) -> str:
        """Get position type name"""
        return MEXCFuturesMapping.POSITION_TYPE.get(position_type, f"UNKNOWN_{position_type}")
    
    @staticmethod
    def get_open_type_name(open_type: int) -> str:
        """Get open type name"""
        return MEXCFuturesMapping.OPEN_TYPE.get(open_type, f"UNKNOWN_{open_type}")
    
    @staticmethod
    def get_order_side_name(side: int) -> str:
        """Get order side name"""
        return MEXCFuturesMapping.ORDER_SIDE.get(side, f"UNKNOWN_{side}")
    
    @staticmethod
    def get_order_state_name(state: int) -> str:
        """Get order state name"""
        return MEXCFuturesMapping.ORDER_STATE.get(state, f"UNKNOWN_{state}")
    
    @staticmethod
    def get_error_message(error_code: int) -> str:
        """Get error message"""
        return MEXCFuturesMapping.ERROR_CODES.get(error_code, f"UNKNOWN_ERROR_{error_code}")

# CONTRACT SIZE MAPPING (based on official documentation and real data)
CONTRACT_SPECS = {
    "BTC_USDT": {
        "contract_size": 0.0001,  # From official docs: contractSize: 0.0001
        "base_asset": "BTC",
        "quote_asset": "USDT",
        "price_scale": 2,
        "vol_scale": 0,
        "min_vol": 1,
        "vol_unit": 1,
        "conversion_formula": "contracts * 0.001"  # Real data shows: 8 contracts = 0.008 BTC
    },
    "PAXG_USDT": {
        "contract_size": 0.001,   # Estimated based on real data: 1000 contracts = 1 PAXG
        "base_asset": "PAXG",
        "quote_asset": "USDT",
        "price_scale": 2,
        "vol_scale": 0,
        "min_vol": 1000,
        "vol_unit": 1000,
        "conversion_formula": "contracts / 1000"  # Real data shows: 1000 contracts = 1.0 PAXG
    }
}

# BALANCE FIELD MAPPING (from real API responses)
BALANCE_FIELDS = {
    "currency": "Currency symbol (e.g., USDT)",
    "positionMargin": "Position margin amount",
    "availableBalance": "Available balance for trading",
    "cashBalance": "Cash balance (actual wallet balance)",
    "frozenBalance": "Frozen balance",
    "equity": "Total equity (asset value including P&L)",
    "unrealized": "Unrealized P&L",
    "bonus": "Bonus amount"
}

def convert_contract_volume_to_asset(symbol: str, contract_volume: float) -> float:
    """Convert contract volume to actual asset amount"""
    if symbol == "BTC_USDT":
        # Real data: 8 contracts = 0.008 BTC, so 1 contract = 0.001 BTC
        return contract_volume * 0.001
    elif symbol == "PAXG_USDT":
        # Real data: 1000 contracts = 1.0 PAXG, so 1 contract = 0.001 PAXG
        return contract_volume / 1000.0
    else:
        # Default: assume 1 contract = 0.001 of base asset
        return contract_volume * 0.001

def get_asset_unit(symbol: str) -> str:
    """Get asset unit for display"""
    if symbol in CONTRACT_SPECS:
        return CONTRACT_SPECS[symbol]["base_asset"]
    else:
        return symbol.split('_')[0]

if __name__ == "__main__":
    # Test the mapping
    print("🧪 TESTING MEXC FUTURES API MAPPING")
    print("=" * 50)
    
    # Test position type
    print("Position Types:")
    for code, name in MEXCFuturesMapping.POSITION_TYPE.items():
        print(f"  {code}: {name}")
    
    # Test order side
    print("\nOrder Sides:")
    for code, name in MEXCFuturesMapping.ORDER_SIDE.items():
        print(f"  {code}: {name}")
    
    # Test volume conversion
    print("\nVolume Conversion:")
    btc_contracts = 8
    btc_volume = convert_contract_volume_to_asset("BTC_USDT", btc_contracts)
    print(f"  BTC_USDT: {btc_contracts} contracts = {btc_volume:.4f} {get_asset_unit('BTC_USDT')}")
    
    paxg_contracts = 1000
    paxg_volume = convert_contract_volume_to_asset("PAXG_USDT", paxg_contracts)
    print(f"  PAXG_USDT: {paxg_contracts} contracts = {paxg_volume:.4f} {get_asset_unit('PAXG_USDT')}")
