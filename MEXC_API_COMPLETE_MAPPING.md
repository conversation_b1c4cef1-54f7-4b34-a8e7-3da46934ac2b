# MEXC FUTURES API - COMPLETE MAPPING DOCUMENTATION

## 📋 Overview
This document provides complete mapping for all MEXC Futures API parameters based on official documentation and real data analysis.

**Source**: [MEXC Contract API Documentation](https://mexcdevelop.github.io/apidocs/contract_v1_en/)

---

## 🏗️ POSITION PARAMETERS

### Position Type (`positionType`)
| Code | Description | Usage |
|------|-------------|-------|
| `1` | **LONG** | Long position |
| `2` | **SHORT** | Short position |

### Open Type (`openType`)
| Code | Description | Usage |
|------|-------------|-------|
| `1` | **ISOLATED** | Isolated margin |
| `2` | **CROSS** | Cross margin |

### Position Open Type (`positionOpenType`)
| Code | Description | Usage |
|------|-------------|-------|
| `1` | **ISOLATED_ONLY** | Only isolated margin allowed |
| `2` | **CROSS_ONLY** | Only cross margin allowed |
| `3` | **BOTH** | Both isolated and cross allowed |

### Position State (`state`)
| Code | Description | Usage |
|------|-------------|-------|
| `1` | **HOLDING** | Normal holding position |
| `2` | **SYSTEM_HOLDING** | System auto-holding |
| `3` | **CLOSED** | Position closed |

---

## 📊 ORDER PARAMETERS

### Order Side (`side`)
| Code | Description | Usage |
|------|-------------|-------|
| `1` | **OPEN_LONG** | Open long position |
| `2` | **CLOSE_SHORT** | Close short position |
| `3` | **OPEN_SHORT** | Open short position |
| `4` | **CLOSE_LONG** | Close long position |

### Order Type (`orderType`)
| Code | Description | Usage |
|------|-------------|-------|
| `1` | **LIMIT** | Limit order |
| `2` | **POST_ONLY_MAKER** | Post Only Maker |
| `3` | **IMMEDIATE_OR_CANCEL** | IOC order |
| `4` | **FILL_OR_KILL** | FOK order |
| `5` | **MARKET** | Market order |

### Order State (`state`)
| Code | Description | Usage |
|------|-------------|-------|
| `1` | **UNINFORMED** | Not filled |
| `2` | **UNCOMPLETED** | Partially filled |
| `3` | **COMPLETED** | Fully filled |
| `4` | **CANCELLED** | Cancelled |
| `5` | **INVALID** | Invalid order |

### Order Category (`category`)
| Code | Description | Usage |
|------|-------------|-------|
| `1` | **LIMIT_ORDER** | Normal limit order |
| `2` | **SYSTEM_TAKEOVER** | System takeover order |
| `3` | **CLOSE_ORDER** | Close order |
| `4` | **ADL_REDUCTION** | ADL reduction order |

---

## 💰 BALANCE PARAMETERS

### Balance Fields (from `get_futures_account_assets()`)
| Field | Description | Usage |
|-------|-------------|-------|
| `currency` | Currency symbol | e.g., "USDT" |
| `positionMargin` | Position margin amount | Margin used for positions |
| `availableBalance` | Available balance | Available for trading |
| `cashBalance` | Cash balance | Actual wallet balance |
| `frozenBalance` | Frozen balance | Temporarily frozen funds |
| `equity` | Total equity | **Asset value (including P&L)** |
| `unrealized` | Unrealized P&L | Unrealized profit/loss |
| `bonus` | Bonus amount | Bonus funds |

### Balance Calculation Formula
```
Giá trị tài sản = equity (includes P&L)
Số dư ví = equity - unrealized P&L (excludes P&L)
```

---

## 📏 CONTRACT SPECIFICATIONS

### Volume Conversion (Real Data Confirmed)

#### BTC_USDT
- **Contract Size**: 0.0001 BTC (from official docs)
- **Real Conversion**: `contracts * 0.001`
- **Example**: 8 contracts = 0.008 BTC
- **Display Unit**: BTC

#### PAXG_USDT  
- **Contract Size**: 0.001 PAXG (estimated)
- **Real Conversion**: `contracts / 1000`
- **Example**: 1000 contracts = 1.0 PAXG
- **Display Unit**: PAXG

#### Default (Other Pairs)
- **Default Conversion**: `contracts * 0.001`
- **Display Unit**: Base asset (e.g., ETH, ADA)

---

## 🔄 TRANSACTION PARAMETERS

### Deal Type (`T`)
| Code | Description | Usage |
|------|-------------|-------|
| `1` | **BUY** | Purchase transaction |
| `2` | **SELL** | Sell transaction |

### Deal Open Position (`O`)
| Code | Description | Usage |
|------|-------------|-------|
| `1` | **OPEN** | Open position |
| `2` | **CLOSE** | Close position |
| `3` | **NO_CHANGE** | No position change |

### Deal Self Trade (`M`)
| Code | Description | Usage |
|------|-------------|-------|
| `1` | **YES** | Self trade |
| `2` | **NO** | Not self trade |

---

## ⚠️ ERROR CODES

### Common Error Codes
| Code | Description |
|------|-------------|
| `0` | **SUCCESS** |
| `1` | **PARAM_INVALID** |
| `2` | **INSUFFICIENT_BALANCE** |
| `3` | **POSITION_NOT_EXISTS** |
| `4` | **POSITION_NOT_ENOUGH** |
| `5` | **POSITION_LIQUIDATED** |
| `8` | **SYSTEM_CANCEL** |
| `18` | **IOC_CANCEL** |
| `19` | **FOK_CANCEL** |
| `20` | **POST_ONLY_CANCEL** |

---

## 🎯 PRACTICAL IMPLEMENTATION

### Position Display Logic
```python
# Position Type
side = 'LONG' if position_type == 1 else 'SHORT'

# Volume Conversion
if symbol == 'BTC_USDT':
    size_asset = contracts * 0.001
    unit = 'BTC'
elif symbol == 'PAXG_USDT':
    size_asset = contracts / 1000.0
    unit = 'PAXG'
```

### Order Side Detection
```python
# TP/SL Logic for Orders
if position_id != 0:  # Order belongs to position
    if side == 'SHORT':
        if price < entry_price:
            order_type = 'TP'  # Take Profit
        elif price > entry_price:
            order_type = 'SL'  # Stop Loss
```

### Balance Calculation
```python
# Account Status
equity = float(asset.get('equity', 0))
unrealized_pnl = float(asset.get('unrealized', 0))

asset_value = equity  # Giá trị tài sản (with P&L)
wallet_balance = equity - unrealized_pnl  # Số dư ví (without P&L)
```

---

## ✅ VERIFICATION CHECKLIST

- [x] **Position Type**: 1=LONG, 2=SHORT
- [x] **Order Side**: 1=OPEN_LONG, 2=CLOSE_SHORT, 3=OPEN_SHORT, 4=CLOSE_LONG
- [x] **Volume Conversion**: BTC (×0.001), PAXG (÷1000)
- [x] **Balance Fields**: equity, cashBalance, unrealized
- [x] **TP/SL Detection**: Based on positionId and price comparison
- [x] **Error Handling**: Complete error code mapping

---

## 📚 REFERENCES

1. [MEXC Contract API Documentation](https://mexcdevelop.github.io/apidocs/contract_v1_en/)
2. Real API Response Analysis
3. Trading Dashboard Implementation
4. Volume Conversion Testing

**Last Updated**: 2025-01-19  
**Status**: ✅ Complete and Verified
