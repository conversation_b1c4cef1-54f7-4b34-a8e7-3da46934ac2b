#!/usr/bin/env python3
import requests
import json

def get_contract_details(symbol):
    """Get contract specifications from MEXC API"""
    try:
        url = f"https://contract.mexc.com/api/v1/contract/detail/{symbol}"
        response = requests.get(url, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print(f"📊 {symbol} Contract Details:")
            if data.get('success'):
                contract_info = data.get('data', {})
                print(f"  Contract Size: {contract_info.get('contractSize', 'N/A')}")
                print(f"  Price Scale: {contract_info.get('priceScale', 'N/A')}")
                print(f"  Vol Scale: {contract_info.get('volScale', 'N/A')}")
                print(f"  Min Vol: {contract_info.get('minVol', 'N/A')}")
                print(f"  Max Vol: {contract_info.get('maxVol', 'N/A')}")
                print(f"  Symbol: {contract_info.get('symbol', 'N/A')}")
                print(f"  Base Coin: {contract_info.get('baseCoin', 'N/A')}")
                print(f"  Quote Coin: {contract_info.get('quoteCoin', 'N/A')}")
                return contract_info
            else:
                print(f"  API Error: {data}")
                return None
        else:
            print(f"  HTTP Error: {response.status_code}")
            return None
    except Exception as e:
        print(f"  Exception: {e}")
        return None

def calculate_volume_examples():
    """Calculate volume examples based on contract specifications"""
    print("\n🧮 VOLUME CALCULATION EXAMPLES:")
    print("=" * 50)
    
    # Example: BTC_USDT position with 8 contracts
    print("Example 1: BTC_USDT Position")
    print("  Contracts from API: 8")
    print("  If 1 contract = 0.0001 BTC:")
    print(f"    Actual BTC volume: {8 * 0.0001:.4f} BTC")
    print("  If 1 contract = 0.001 BTC:")
    print(f"    Actual BTC volume: {8 * 0.001:.4f} BTC")
    print("  If 1 contract = 1 USD:")
    print(f"    USD value: ${8 * 1:.2f}")
    
    print("\nExample 2: PAXG_USDT Position")
    print("  Contracts from API: 1000")
    print("  If 1 contract = 0.001 PAXG:")
    print(f"    Actual PAXG volume: {1000 * 0.001:.4f} PAXG")
    print("  If 1000 contracts = 1 PAXG:")
    print(f"    Actual PAXG volume: {1000 / 1000:.4f} PAXG")

if __name__ == "__main__":
    print("🔍 CHECKING MEXC CONTRACT SPECIFICATIONS...")
    print("=" * 60)
    
    # Check BTC_USDT contract
    btc_info = get_contract_details("BTC_USDT")
    print()
    
    # Check PAXG_USDT contract
    paxg_info = get_contract_details("PAXG_USDT")
    
    # Show calculation examples
    calculate_volume_examples()
    
    print("\n" + "=" * 60)
