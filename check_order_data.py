#!/usr/bin/env python3
import asyncio
import json
from mexc_client import MEXCClient

async def check_order_data():
    print("🔍 CHECKING MEXC ORDER DATA...")
    print("=" * 60)
    
    async with MEXCClient() as client:
        # Check Open Orders
        print("\n📋 OPEN ORDERS:")
        orders = await client.get_futures_open_orders()
        if orders:
            print(f"Found {len(orders)} open orders:")
            for i, order in enumerate(orders, 1):
                print(f"\nOrder {i}:")
                print(f"  Symbol: {order.get('symbol', 'N/A')}")
                print(f"  Side Code: {order.get('side', 'N/A')}")
                print(f"  Order Type: {order.get('orderType', 'N/A')}")
                print(f"  Price: {order.get('price', 'N/A')}")
                print(f"  Volume: {order.get('vol', 'N/A')}")
                print(f"  Position ID: {order.get('positionId', 'N/A')}")
                print("  Full data:")
                print(json.dumps(order, indent=4))
        else:
            print("No open orders found")
        
        print("\n" + "=" * 60)

if __name__ == "__main__":
    asyncio.run(check_order_data())
